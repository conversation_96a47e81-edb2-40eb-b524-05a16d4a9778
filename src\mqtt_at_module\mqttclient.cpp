#include "mqttclient.h"
#include <QDebug>
#include <QTimer>

Q_LOGGING_CATEGORY(mqttClientLog, "mqtt.client")

MqttClient::MqttClient(int clientIdx, QObject *parent)
    : QObject(parent)
    , m_clientIdx(clientIdx)
    , m_port(0)
    , m_state(IDLE)
    , m_mqttClient(nullptr)
    , m_urcTimer(new QTimer(this))
{
    qCDebug(mqttClientLog) << "Creating MQTT client with index:" << m_clientIdx;
    
    // Initialize MQTT client
    m_mqttClient = new QMqttClient(this);
    
    // Connect MQTT client signals
    connect(m_mqttClient, &QMqttClient::connected, 
            this, &MqttClient::onMqttConnected);
    connect(m_mqttClient, &QMqttClient::disconnected, 
            this, &MqttClient::onMqttDisconnected);
    connect(m_mqttClient, &QMqttClient::errorChanged, 
            this, &MqttClient::onMqttError);
    connect(m_mqttClient, &QMqttClient::messageReceived, 
            this, &MqttClient::onMessageReceived);
    
    // Configure URC timer
    m_urcTimer->setSingleShot(true);
    
    qCDebug(mqttClientLog) << "MQTT client" << m_clientIdx << "initialized successfully";
}

MqttClient::~MqttClient()
{
    qCDebug(mqttClientLog) << "Destroying MQTT client" << m_clientIdx;
    
    if (m_mqttClient && m_mqttClient->state() == QMqttClient::Connected) {
        qCDebug(mqttClientLog) << "Disconnecting MQTT client" << m_clientIdx << "during destruction";
        m_mqttClient->disconnectFromHost();
    }
}

bool MqttClient::openNetwork(const QString& hostname, int port)
{
    qCDebug(mqttClientLog) << "AT+QMTOPEN: Client" << m_clientIdx 
                          << "opening network to" << hostname << ":" << port;
    
    // Validate parameters
    if (hostname.isEmpty() || port <= 0 || port > 65535) {
        qCWarning(mqttClientLog) << "Invalid parameters for openNetwork:" << hostname << port;
        return false;
    }
    
    // Check current state
    if (m_state != IDLE && m_state != NET_READY) {
        qCWarning(mqttClientLog) << "Cannot open network in current state:" << m_state;
        return false;
    }
    
    // Store connection parameters
    m_hostname = hostname;
    m_port = port;
    
    // Update state
    setState(NET_READY);
    
    // Emit async URC after short delay to simulate real module behavior
    emitNetworkOpenedAsync(0); // 0 = success
    
    qCDebug(mqttClientLog) << "Network configuration saved for client" << m_clientIdx;
    return true;
}

bool MqttClient::connectMqtt(const QString& clientId, const QString& username, const QString& password)
{
    qCDebug(mqttClientLog) << "AT+QMTCONN: Client" << m_clientIdx 
                          << "connecting with clientId:" << clientId;
    
    // Validate parameters
    if (clientId.isEmpty()) {
        qCWarning(mqttClientLog) << "Client ID cannot be empty";
        return false;
    }
    
    // Check current state
    if (m_state != NET_READY) {
        qCWarning(mqttClientLog) << "Cannot connect MQTT in current state:" << m_state 
                                << "- network must be opened first";
        return false;
    }
    
    // Store MQTT parameters
    m_clientId = clientId;
    m_username = username;
    m_password = password;
    
    // Update state
    setState(CONNECTING);
    
    // Configure MQTT client
    m_mqttClient->setHostname(m_hostname);
    m_mqttClient->setPort(static_cast<quint16>(m_port));
    m_mqttClient->setClientId(m_clientId);
    
    if (!m_username.isEmpty()) {
        m_mqttClient->setUsername(m_username);
    }
    if (!m_password.isEmpty()) {
        m_mqttClient->setPassword(m_password);
    }
    
    // Initiate connection (this combines TCP + MQTT connection)
    qCDebug(mqttClientLog) << "Initiating MQTT connection for client" << m_clientIdx;
    m_mqttClient->connectToHost();
    
    return true;
}

bool MqttClient::subscribe(int msgId, const QString& topic, int qos)
{
    qCDebug(mqttClientLog) << "AT+QMTSUB: Client" << m_clientIdx 
                          << "subscribing to topic:" << topic 
                          << "QoS:" << qos << "msgId:" << msgId;
    
    // Validate parameters
    if (topic.isEmpty() || qos < 0 || qos > 2 || msgId < 1 || msgId > 65535) {
        qCWarning(mqttClientLog) << "Invalid subscription parameters";
        return false;
    }
    
    // Check current state
    if (m_state != CONNECTED) {
        qCWarning(mqttClientLog) << "Cannot subscribe in current state:" << m_state;
        return false;
    }
    
    // Perform subscription
    auto subscription = m_mqttClient->subscribe(topic, static_cast<quint8>(qos));
    if (!subscription) {
        qCWarning(mqttClientLog) << "Failed to create subscription for topic:" << topic;
        emit subscribed(m_clientIdx, msgId, 1, 0); // 1 = failure
        return true; // Command was accepted, but operation failed
    }
    
    // Store subscription info
    m_subscriptions[topic] = qos;
    
    // Connect to subscription state change
    connect(subscription, &QMqttSubscription::stateChanged, 
            [this, msgId, topic, qos](QMqttSubscription::SubscriptionState state) {
        if (state == QMqttSubscription::Subscribed) {
            qCDebug(mqttClientLog) << "Successfully subscribed to topic:" << topic;
            emit subscribed(m_clientIdx, msgId, 0, qos); // 0 = success
        } else if (state == QMqttSubscription::Error) {
            qCWarning(mqttClientLog) << "Subscription failed for topic:" << topic;
            m_subscriptions.remove(topic);
            emit subscribed(m_clientIdx, msgId, 1, 0); // 1 = failure
        }
    });
    
    return true;
}
